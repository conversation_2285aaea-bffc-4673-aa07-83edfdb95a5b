<?php
/**
 * Main Configuration File
 * ERP Management System
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Application constants
define('APP_NAME', 'ERP Management System');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/erp');
define('BASE_PATH', dirname(dirname(__FILE__)));
define('FRONTEND_PATH', dirname(BASE_PATH) . '/frontend');

// API constants
define('API_VERSION', 'v1');
define('API_BASE_URL', APP_URL . '/backend/api');

// Security constants
define('JWT_SECRET', 'your-secret-key-here-change-in-production');
define('ENCRYPTION_KEY', 'your-encryption-key-here');
define('SESSION_TIMEOUT', 3600); // 1 hour

// File upload constants
define('UPLOAD_PATH', BASE_PATH . '/uploads');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx']);

// Pagination constants
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// Email configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'ERP System');

// Company information
define('COMPANY_NAME', 'Your Company Name');
define('COMPANY_ADDRESS', 'Your Company Address');
define('COMPANY_PHONE', '+91-XXXXXXXXXX');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_GST', 'GSTIN Number');

// Module status (enable/disable modules)
define('MODULES', [
    'quality-management' => true,
    'purchase-management' => true,
    'accounts' => true,
    'sales' => true,
    'finances' => true,
    'crm' => true,
    'hrm' => true,
    'resource-management' => true,
    'asset-management' => true,
    'compliance-management' => true,
    'delivery-management' => true,
    'challan-management' => true,
    'production-management' => true,
    'plc-integration' => true
]);

// Include required files
require_once BASE_PATH . '/config/database.php';
require_once BASE_PATH . '/includes/functions.php';
require_once BASE_PATH . '/includes/auth.php';

// CORS headers for API
function setCorsHeaders() {
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
    
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// Set JSON response headers
function setJsonHeaders() {
    header('Content-Type: application/json');
    setCorsHeaders();
}

// Environment detection
function isProduction() {
    return isset($_SERVER['HTTP_HOST']) && 
           !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', '::1']);
}

// Debug mode
define('DEBUG_MODE', !isProduction());

if (DEBUG_MODE) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}

// Autoloader for classes
spl_autoload_register(function ($class_name) {
    $class_file = BASE_PATH . '/classes/' . $class_name . '.php';
    if (file_exists($class_file)) {
        require_once $class_file;
    }
});

// Global exception handler
set_exception_handler(function($exception) {
    error_log("Uncaught exception: " . $exception->getMessage());
    
    if (DEBUG_MODE) {
        echo json_encode([
            'success' => false,
            'message' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'An internal error occurred'
        ]);
    }
});

// Global error handler
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    error_log("Error: $message in $file on line $line");
    
    if (DEBUG_MODE) {
        echo json_encode([
            'success' => false,
            'message' => $message,
            'file' => $file,
            'line' => $line
        ]);
    }
    
    return true;
});

?>
