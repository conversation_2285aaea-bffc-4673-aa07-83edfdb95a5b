<!-- Quality Checks Page -->
<div class="space-y-6">
    <!-- Header Actions -->
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">Quality Checks</h2>
            <p class="text-gray-600">Manage and perform quality control checks</p>
        </div>
        <div class="flex space-x-3">
            <button class="btn-primary">
                <i class="fas fa-plus mr-2"></i>
                New Quality Check
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download mr-2"></i>
                Export Data
            </button>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Filters</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="form-label">Date Range</label>
                <select class="form-input">
                    <option>Last 7 days</option>
                    <option>Last 30 days</option>
                    <option>Last 3 months</option>
                    <option>Custom range</option>
                </select>
            </div>
            <div>
                <label class="form-label">Status</label>
                <select class="form-input">
                    <option>All Status</option>
                    <option>Passed</option>
                    <option>Failed</option>
                    <option>Pending</option>
                </select>
            </div>
            <div>
                <label class="form-label">Inspector</label>
                <select class="form-input">
                    <option>All Inspectors</option>
                    <option>John Smith</option>
                    <option>Sarah Johnson</option>
                    <option>Mike Wilson</option>
                </select>
            </div>
            <div>
                <label class="form-label">Product Category</label>
                <select class="form-input">
                    <option>All Categories</option>
                    <option>Widgets</option>
                    <option>Components</option>
                    <option>Assemblies</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Quality Checks Table -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Quality Check Records</h3>
                <div class="flex space-x-2">
                    <input type="text" placeholder="Search checks..." class="form-input text-sm w-48">
                    <button class="btn-primary text-sm">Search</button>
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                    <tr>
                        <th>Check ID</th>
                        <th>Product</th>
                        <th>Batch/Serial</th>
                        <th>Inspector</th>
                        <th>Check Date</th>
                        <th>Parameters</th>
                        <th>Score</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="font-medium">#QC-001</td>
                        <td>Widget A-100</td>
                        <td>BATCH-2024-001</td>
                        <td>John Smith</td>
                        <td>2024-01-15 10:30</td>
                        <td>Dimensions, Weight, Finish</td>
                        <td>95%</td>
                        <td><span class="badge badge-success">Passed</span></td>
                        <td>
                            <div class="flex space-x-2">
                                <button class="text-blue-600 hover:text-blue-800" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-green-600 hover:text-green-800" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-purple-600 hover:text-purple-800" title="Print Report">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="font-medium">#QC-002</td>
                        <td>Component B-200</td>
                        <td>BATCH-2024-002</td>
                        <td>Sarah Johnson</td>
                        <td>2024-01-15 14:15</td>
                        <td>Electrical, Thermal</td>
                        <td>67%</td>
                        <td><span class="badge badge-error">Failed</span></td>
                        <td>
                            <div class="flex space-x-2">
                                <button class="text-blue-600 hover:text-blue-800" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-green-600 hover:text-green-800" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-red-600 hover:text-red-800" title="Non-Conformance">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="font-medium">#QC-003</td>
                        <td>Assembly C-300</td>
                        <td>BATCH-2024-003</td>
                        <td>Mike Wilson</td>
                        <td>2024-01-14 16:45</td>
                        <td>Functional, Safety</td>
                        <td>-</td>
                        <td><span class="badge badge-warning">Pending</span></td>
                        <td>
                            <div class="flex space-x-2">
                                <button class="text-blue-600 hover:text-blue-800" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-green-600 hover:text-green-800" title="Complete Check">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="text-orange-600 hover:text-orange-800" title="Reschedule">
                                    <i class="fas fa-calendar"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="p-6 border-t border-gray-200">
            <div class="flex justify-between items-center">
                <p class="text-sm text-gray-600">Showing 1 to 3 of 156 entries</p>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Previous</button>
                    <button class="px-3 py-1 bg-primary text-white rounded text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quality Check Templates -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Quality Check Templates</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition-colors duration-200">
                <div class="flex items-center space-x-3 mb-3">
                    <i class="fas fa-clipboard-check text-primary text-xl"></i>
                    <h4 class="font-medium">Dimensional Check</h4>
                </div>
                <p class="text-sm text-gray-600 mb-3">Standard dimensional quality check template for manufactured parts</p>
                <button class="btn-primary text-sm w-full">Use Template</button>
            </div>
            <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition-colors duration-200">
                <div class="flex items-center space-x-3 mb-3">
                    <i class="fas fa-bolt text-primary text-xl"></i>
                    <h4 class="font-medium">Electrical Test</h4>
                </div>
                <p class="text-sm text-gray-600 mb-3">Electrical safety and performance testing template</p>
                <button class="btn-primary text-sm w-full">Use Template</button>
            </div>
            <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition-colors duration-200">
                <div class="flex items-center space-x-3 mb-3">
                    <i class="fas fa-shield-alt text-primary text-xl"></i>
                    <h4 class="font-medium">Safety Check</h4>
                </div>
                <p class="text-sm text-gray-600 mb-3">Comprehensive safety compliance check template</p>
                <button class="btn-primary text-sm w-full">Use Template</button>
            </div>
        </div>
    </div>
</div>
