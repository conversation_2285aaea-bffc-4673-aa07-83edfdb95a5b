// ERP System Main JavaScript File

// Global variables
let isLoading = false;

// DOM Elements
const sidebar = document.getElementById("sidebar");
const sidebarOverlay = document.getElementById("sidebar-overlay");
const mobileMenuBtn = document.getElementById("mobile-menu-btn");
const loading = document.getElementById("loading");

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
  setupEventListeners();
});

// Initialize application
function initializeApp() {
  console.log("ERP System initialized");

  // Check for saved user preferences
  loadUserPreferences();

  // Initialize UI components
  initializeUIComponents();
}

// Setup event listeners
function setupEventListeners() {
  // Mobile menu toggle
  if (mobileMenuBtn) {
    mobileMenuBtn.addEventListener("click", toggleMobileSidebar);
  }

  // Sidebar overlay click
  if (sidebarOverlay) {
    sidebarOverlay.addEventListener("click", closeMobileSidebar);
  }

  // Window resize handler
  window.addEventListener("resize", handleWindowResize);

  // Keyboard shortcuts
  document.addEventListener("keydown", handleKeyboardShortcuts);
}

// Toggle mobile sidebar
function toggleMobileSidebar() {
  sidebar.classList.toggle("-translate-x-full");
  sidebarOverlay.classList.toggle("hidden");
}

// Close mobile sidebar
function closeMobileSidebar() {
  sidebar.classList.add("-translate-x-full");
  sidebarOverlay.classList.add("hidden");
}

// Handle window resize
function handleWindowResize() {
  if (window.innerWidth >= 768) {
    closeMobileSidebar();
  }
}

// Handle keyboard shortcuts
function handleKeyboardShortcuts(e) {
  // Ctrl/Cmd + K for search (future implementation)
  if ((e.ctrlKey || e.metaKey) && e.key === "k") {
    e.preventDefault();
    // Open search modal
    console.log("Search shortcut triggered");
  }

  // Escape key to close modals
  if (e.key === "Escape") {
    closeAllModals();
  }
}

// Initialize UI components
function initializeUIComponents() {
  // Initialize any UI components like tooltips, dropdowns, etc.
  console.log("UI components initialized");
}

// Show loading spinner
function showLoading() {
  if (loading) {
    loading.classList.remove("hidden");
  }
}

// Hide loading spinner
function hideLoading() {
  if (loading) {
    loading.classList.add("hidden");
  }
}

// Show error message
function showError(message) {
  showNotification(message, "error");
}

// Show success message
function showSuccess(message) {
  showNotification(message, "success");
}

// Show notification
function showNotification(message, type = "info") {
  const notification = document.createElement("div");
  notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm alert alert-${type} fade-in`;
  notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-lg">&times;</button>
        </div>
    `;

  document.body.appendChild(notification);

  // Auto remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 5000);
}

// Close all modals
function closeAllModals() {
  document.querySelectorAll(".modal").forEach((modal) => {
    modal.classList.add("hidden");
  });
}

// Load user preferences
function loadUserPreferences() {
  // Load from localStorage
  const preferences = localStorage.getItem("erpPreferences");
  if (preferences) {
    try {
      const prefs = JSON.parse(preferences);
      // Apply preferences
      console.log("User preferences loaded:", prefs);
    } catch (error) {
      console.error("Error loading preferences:", error);
    }
  }
}

// Save user preferences
function saveUserPreferences(preferences) {
  try {
    localStorage.setItem("erpPreferences", JSON.stringify(preferences));
  } catch (error) {
    console.error("Error saving preferences:", error);
  }
}

// Utility function for AJAX requests
async function makeRequest(url, options = {}) {
  const defaultOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "X-Requested-With": "XMLHttpRequest",
    },
  };

  const finalOptions = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, finalOptions);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Request failed:", error);
    throw error;
  }
}

// Format currency
function formatCurrency(amount, currency = "₹") {
  return `${currency}${Number(amount).toLocaleString("en-IN")}`;
}

// Format date
function formatDate(date, format = "DD/MM/YYYY") {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, "0");
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const year = d.getFullYear();

  switch (format) {
    case "DD/MM/YYYY":
      return `${day}/${month}/${year}`;
    case "MM/DD/YYYY":
      return `${month}/${day}/${year}`;
    case "YYYY-MM-DD":
      return `${year}-${month}-${day}`;
    default:
      return d.toLocaleDateString();
  }
}

// Debounce function
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Modal functions
function openModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.classList.remove("hidden");
  }
}

function closeModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.classList.add("hidden");
  }
}

// Form validation
function validateForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return false;

  const requiredFields = form.querySelectorAll("[required]");
  let isValid = true;

  requiredFields.forEach((field) => {
    if (!field.value.trim()) {
      field.classList.add("border-red-500");
      isValid = false;
    } else {
      field.classList.remove("border-red-500");
    }
  });

  return isValid;
}

// Show coming soon notification
function showComingSoon(moduleName) {
  showNotification(`${moduleName} module is coming soon!`, "info");
}

// Export functions for global use
window.openModal = openModal;
window.closeModal = closeModal;
window.validateForm = validateForm;
window.showError = showError;
window.showSuccess = showSuccess;
window.showNotification = showNotification;
window.makeRequest = makeRequest;
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.showComingSoon = showComingSoon;
