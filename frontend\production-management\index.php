<?php
$module_name = 'Production Management';
$page_title = 'Production Management';
$current_page = $_GET['page'] ?? 'dashboard';

// Define sub-menu items for Production Management
$sub_menus = [
    'dashboard' => ['title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
    'production-orders' => ['title' => 'Production Orders', 'icon' => 'fas fa-clipboard-list'],
    'work-orders' => ['title' => 'Work Orders', 'icon' => 'fas fa-tasks'],
    'production-lines' => ['title' => 'Production Lines', 'icon' => 'fas fa-industry'],
    'scheduling' => ['title' => 'Production Scheduling', 'icon' => 'fas fa-calendar-alt'],
    'capacity-planning' => ['title' => 'Capacity Planning', 'icon' => 'fas fa-chart-area'],
    'machine-monitoring' => ['title' => 'Machine Monitoring', 'icon' => 'fas fa-desktop'],
    'maintenance' => ['title' => 'Maintenance', 'icon' => 'fas fa-tools'],
    'quality-control' => ['title' => 'Quality Control', 'icon' => 'fas fa-check-circle'],
    'inventory' => ['title' => 'Production Inventory', 'icon' => 'fas fa-boxes'],
    'reports' => ['title' => 'Production Reports', 'icon' => 'fas fa-chart-bar'],
    'settings' => ['title' => 'Settings', 'icon' => 'fas fa-cog']
];

include '../includes/header.php';
?>

<!-- Sidebar -->
<div id="sidebar" class="bg-sidebar text-white w-64 min-h-screen transform -translate-x-full md:translate-x-0 transition-transform duration-300 ease-in-out fixed md:relative z-30">
    <!-- Sidebar Header -->
    <div class="p-4 border-b border-gray-700">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <i class="fas fa-cogs text-white text-lg"></i>
            </div>
            <div>
                <h1 class="text-xl font-bold">Production Management</h1>
                <p class="text-sm text-gray-400">Manufacturing System</p>
            </div>
        </div>
    </div>

    <!-- Back to Main -->
    <div class="p-4 border-b border-gray-700">
        <a href="../index.php" class="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors duration-200">
            <i class="fas fa-arrow-left"></i>
            <span>Back to Main Dashboard</span>
        </a>
    </div>

    <!-- Navigation Menu -->
    <nav class="mt-4 px-4">
        <div class="space-y-2">
            <?php foreach ($sub_menus as $key => $menu): ?>
            <a href="?page=<?php echo $key; ?>" 
               class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200 <?php echo $current_page == $key ? 'active' : ''; ?>">
                <i class="<?php echo $menu['icon']; ?> w-5"></i>
                <span><?php echo $menu['title']; ?></span>
            </a>
            <?php endforeach; ?>
        </div>
    </nav>
</div>

<!-- Main Content Area -->
<div class="flex-1 flex flex-col overflow-hidden">
    <!-- Top Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <!-- Mobile menu button -->
                <button id="mobile-menu-btn" class="md:hidden text-gray-600 hover:text-gray-900">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                
                <div>
                    <h2 class="text-2xl font-semibold text-gray-800">
                        <?php echo $sub_menus[$current_page]['title'] ?? 'Production Management'; ?>
                    </h2>
                    <p class="text-sm text-gray-600">Production Management Module</p>
                </div>
            </div>

            <div class="flex items-center space-x-4">
                <!-- Notifications -->
                <button class="relative text-gray-600 hover:text-gray-900">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">2</span>
                </button>

                <!-- User Profile -->
                <div class="relative">
                    <button class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                        <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <span class="hidden md:block">Admin</span>
                        <i class="fas fa-chevron-down text-sm"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-y-auto p-6">
        <?php
        // Include the appropriate page content
        $page_file = "pages/{$current_page}.php";
        if (file_exists($page_file)) {
            include $page_file;
        } else {
            // Default dashboard content
            include 'pages/dashboard.php';
        }
        ?>
    </main>
</div>

<?php include '../includes/footer.php'; ?>
