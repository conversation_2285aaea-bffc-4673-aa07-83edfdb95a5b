<?php
/**
 * Authentication Functions
 * ERP Management System
 */

/**
 * User login
 */
function loginUser($email, $password) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Get user by email
        $query = "SELECT * FROM users WHERE email = :email AND status = 'active'";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception('Invalid email or password');
        }
        
        // Verify password
        if (!verifyPassword($password, $user['password'])) {
            throw new Exception('Invalid email or password');
        }
        
        // Update last login
        $update_query = "UPDATE users SET last_login = NOW() WHERE id = :user_id";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindParam(':user_id', $user['id']);
        $update_stmt->execute();
        
        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['login_time'] = time();
        
        // Log activity
        logActivity($user['id'], 'login', 'auth', 'User logged in');
        
        return [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => $user['role'],
            'permissions' => getUserPermissions($user['id'])
        ];
        
    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * User logout
 */
function logoutUser() {
    $user_id = getCurrentUserId();
    
    if ($user_id) {
        logActivity($user_id, 'logout', 'auth', 'User logged out');
    }
    
    // Destroy session
    session_destroy();
    session_start();
    
    return true;
}

/**
 * Register new user
 */
function registerUser($data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Validate required fields
        $required_fields = ['name', 'email', 'password', 'role'];
        $missing_fields = validateRequiredFields($data, $required_fields);
        
        if (!empty($missing_fields)) {
            throw new Exception('Missing required fields: ' . implode(', ', $missing_fields));
        }
        
        // Check if email already exists
        $check_query = "SELECT id FROM users WHERE email = :email";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bindParam(':email', $data['email']);
        $check_stmt->execute();
        
        if ($check_stmt->fetch()) {
            throw new Exception('Email already exists');
        }
        
        // Hash password
        $hashed_password = hashPassword($data['password']);
        
        // Insert user
        $query = "INSERT INTO users (name, email, password, role, status, created_at) 
                  VALUES (:name, :email, :password, :role, 'active', NOW())";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':role', $data['role']);
        
        if ($stmt->execute()) {
            $user_id = $conn->lastInsertId();
            
            // Log activity
            logActivity($user_id, 'register', 'auth', 'User registered');
            
            return [
                'id' => $user_id,
                'name' => $data['name'],
                'email' => $data['email'],
                'role' => $data['role']
            ];
        } else {
            throw new Exception('Failed to create user');
        }
        
    } catch (Exception $e) {
        error_log("Registration error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Change user password
 */
function changePassword($user_id, $current_password, $new_password) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Get current user
        $query = "SELECT password FROM users WHERE id = :user_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception('User not found');
        }
        
        // Verify current password
        if (!verifyPassword($current_password, $user['password'])) {
            throw new Exception('Current password is incorrect');
        }
        
        // Update password
        $hashed_password = hashPassword($new_password);
        
        $update_query = "UPDATE users SET password = :password, updated_at = NOW() WHERE id = :user_id";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindParam(':password', $hashed_password);
        $update_stmt->bindParam(':user_id', $user_id);
        
        if ($update_stmt->execute()) {
            logActivity($user_id, 'password_change', 'auth', 'Password changed');
            return true;
        } else {
            throw new Exception('Failed to update password');
        }
        
    } catch (Exception $e) {
        error_log("Password change error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Reset password
 */
function resetPassword($email) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Check if user exists
        $query = "SELECT id, name FROM users WHERE email = :email AND status = 'active'";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception('User not found');
        }
        
        // Generate reset token
        $reset_token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        // Save reset token
        $token_query = "INSERT INTO password_resets (user_id, token, expires_at, created_at) 
                        VALUES (:user_id, :token, :expires_at, NOW())
                        ON DUPLICATE KEY UPDATE 
                        token = :token, expires_at = :expires_at, created_at = NOW()";
        
        $token_stmt = $conn->prepare($token_query);
        $token_stmt->bindParam(':user_id', $user['id']);
        $token_stmt->bindParam(':token', $reset_token);
        $token_stmt->bindParam(':expires_at', $expires_at);
        
        if ($token_stmt->execute()) {
            // Send reset email
            $reset_link = APP_URL . "/reset-password.php?token=" . $reset_token;
            $subject = "Password Reset - " . APP_NAME;
            $body = "
                <h2>Password Reset Request</h2>
                <p>Hello {$user['name']},</p>
                <p>You have requested to reset your password. Click the link below to reset your password:</p>
                <p><a href='{$reset_link}'>Reset Password</a></p>
                <p>This link will expire in 1 hour.</p>
                <p>If you did not request this, please ignore this email.</p>
            ";
            
            if (sendEmail($email, $subject, $body)) {
                logActivity($user['id'], 'password_reset_request', 'auth', 'Password reset requested');
                return true;
            } else {
                throw new Exception('Failed to send reset email');
            }
        } else {
            throw new Exception('Failed to generate reset token');
        }
        
    } catch (Exception $e) {
        error_log("Password reset error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Verify reset token and update password
 */
function verifyResetToken($token, $new_password) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Check token validity
        $query = "SELECT pr.user_id, u.email 
                  FROM password_resets pr 
                  JOIN users u ON pr.user_id = u.id 
                  WHERE pr.token = :token AND pr.expires_at > NOW()";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':token', $token);
        $stmt->execute();
        
        $reset_data = $stmt->fetch();
        
        if (!$reset_data) {
            throw new Exception('Invalid or expired reset token');
        }
        
        // Update password
        $hashed_password = hashPassword($new_password);
        
        $update_query = "UPDATE users SET password = :password, updated_at = NOW() WHERE id = :user_id";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindParam(':password', $hashed_password);
        $update_stmt->bindParam(':user_id', $reset_data['user_id']);
        
        if ($update_stmt->execute()) {
            // Delete used token
            $delete_query = "DELETE FROM password_resets WHERE user_id = :user_id";
            $delete_stmt = $conn->prepare($delete_query);
            $delete_stmt->bindParam(':user_id', $reset_data['user_id']);
            $delete_stmt->execute();
            
            logActivity($reset_data['user_id'], 'password_reset_complete', 'auth', 'Password reset completed');
            return true;
        } else {
            throw new Exception('Failed to update password');
        }
        
    } catch (Exception $e) {
        error_log("Reset token verification error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Get user permissions
 */
function getUserPermissions($user_id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "SELECT p.name 
                  FROM permissions p 
                  JOIN user_permissions up ON p.id = up.permission_id 
                  WHERE up.user_id = :user_id";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        $permissions = [];
        while ($row = $stmt->fetch()) {
            $permissions[] = $row['name'];
        }
        
        return $permissions;
        
    } catch (Exception $e) {
        error_log("Error getting user permissions: " . $e->getMessage());
        return [];
    }
}

/**
 * Check session timeout
 */
function checkSessionTimeout() {
    if (isset($_SESSION['login_time'])) {
        $session_duration = time() - $_SESSION['login_time'];
        
        if ($session_duration > SESSION_TIMEOUT) {
            logoutUser();
            sendErrorResponse('Session expired', 401);
        }
    }
}

/**
 * Refresh session
 */
function refreshSession() {
    if (isLoggedIn()) {
        $_SESSION['login_time'] = time();
    }
}

?>
