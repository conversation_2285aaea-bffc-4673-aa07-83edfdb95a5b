/* Custom CSS for ERP System */

/* Smooth transitions */
* {
    transition: all 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Navigation active state */
.nav-item.active {
    background-color: #374151;
    border-left: 4px solid #1e40af;
}

/* Card hover effects */
.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button animations */
.btn-primary {
    @apply bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200;
}

.btn-secondary {
    @apply bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200;
}

.btn-success {
    @apply bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors duration-200;
}

.btn-warning {
    @apply bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors duration-200;
}

.btn-danger {
    @apply bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors duration-200;
}

/* Form styles */
.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-group {
    @apply mb-4;
}

/* Table styles */
.table-container {
    @apply overflow-x-auto bg-white rounded-lg shadow;
}

.table {
    @apply min-w-full divide-y divide-gray-200;
}

.table th {
    @apply px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr:hover {
    @apply bg-gray-50;
}

/* Modal styles */
.modal {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
    @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4;
}

.modal-header {
    @apply px-6 py-4 border-b border-gray-200;
}

.modal-body {
    @apply px-6 py-4;
}

.modal-footer {
    @apply px-6 py-4 border-t border-gray-200 flex justify-end space-x-2;
}

/* Alert styles */
.alert {
    @apply p-4 rounded-lg mb-4;
}

.alert-success {
    @apply bg-green-100 border border-green-400 text-green-700;
}

.alert-warning {
    @apply bg-yellow-100 border border-yellow-400 text-yellow-700;
}

.alert-error {
    @apply bg-red-100 border border-red-400 text-red-700;
}

.alert-info {
    @apply bg-blue-100 border border-blue-400 text-blue-700;
}

/* Loading animation */
.loading-spinner {
    @apply animate-spin rounded-full border-b-2 border-primary;
}

/* Responsive utilities */
@media (max-width: 768px) {
    .sidebar-mobile {
        transform: translateX(-100%);
    }
    
    .sidebar-mobile.open {
        transform: translateX(0);
    }
}

/* Custom animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Status badges */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-error {
    @apply bg-red-100 text-red-800;
}

.badge-info {
    @apply bg-blue-100 text-blue-800;
}

.badge-secondary {
    @apply bg-gray-100 text-gray-800;
}

/* Progress bars */
.progress {
    @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar {
    @apply h-2 rounded-full transition-all duration-300;
}

.progress-bar-primary {
    @apply bg-primary;
}

.progress-bar-success {
    @apply bg-green-500;
}

.progress-bar-warning {
    @apply bg-yellow-500;
}

.progress-bar-danger {
    @apply bg-red-500;
}

/* Dropdown styles */
.dropdown {
    @apply relative inline-block;
}

.dropdown-content {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10;
}

.dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100;
}

/* Card styles */
.card {
    @apply bg-white rounded-lg shadow border border-gray-200;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200;
}

/* Sidebar animation */
.sidebar-transition {
    transition: transform 0.3s ease-in-out;
}

/* Focus styles */
.focus-ring:focus {
    @apply outline-none ring-2 ring-primary ring-opacity-50;
}

/* Text utilities */
.text-truncate {
    @apply truncate;
}

/* Spacing utilities */
.section-spacing {
    @apply py-8;
}

.content-spacing {
    @apply space-y-6;
}

/* Border utilities */
.border-dashed {
    border-style: dashed;
}

/* Background patterns */
.bg-pattern {
    background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .nav-item.active {
        border-left-width: 6px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
