<?php
// Get current module from URL parameter
$current_module = isset($_GET['module']) ? $_GET['module'] : 'dashboard';

// Define module information
$modules = [
    'dashboard' => ['title' => 'Dashboard', 'subtitle' => 'Welcome to ERP Management System'],
    'quality-management' => ['title' => 'Quality Management', 'subtitle' => 'Manage quality control and assurance'],
    'purchase-management' => ['title' => 'Purchase Management', 'subtitle' => 'Handle procurement and vendor management'],
    'accounts' => ['title' => 'Accounts', 'subtitle' => 'Manage financial accounts and transactions'],
    'sales' => ['title' => 'Sales', 'subtitle' => 'Track sales orders and customer management'],
    'finances' => ['title' => 'Finances', 'subtitle' => 'Financial planning and analysis'],
    'crm' => ['title' => 'CRM', 'subtitle' => 'Customer relationship management'],
    'hrm' => ['title' => 'HRM', 'subtitle' => 'Human resource management'],
    'resource-management' => ['title' => 'Resource Management', 'subtitle' => 'Manage company resources'],
    'asset-management' => ['title' => 'Asset Management', 'subtitle' => 'Track and manage company assets'],
    'compliance-management' => ['title' => 'Compliance Management', 'subtitle' => 'Ensure regulatory compliance'],
    'delivery-management' => ['title' => 'Delivery Management', 'subtitle' => 'Manage deliveries and logistics'],
    'challan-management' => ['title' => 'Challan Management', 'subtitle' => 'Handle delivery challans'],
    'production-management' => ['title' => 'Production Management', 'subtitle' => 'Manage production processes'],
    'plc-integration' => ['title' => 'PLC Integration', 'subtitle' => 'Industrial automation integration']
];

$page_title = $modules[$current_module]['title'] ?? 'Dashboard';
$page_subtitle = $modules[$current_module]['subtitle'] ?? 'Welcome to ERP Management System';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        <?php echo $page_title; ?> - ERP Management System
    </title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#64748b',
                        accent: '#f59e0b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444',
                        sidebar: '#1f2937',
                        sidebarHover: '#374151'
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50 font-sans">
    <!-- Loading Spinner -->
    <div id="loading" class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
    </div>

    <!-- Main Container -->
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar"
            class="bg-sidebar text-white w-64 min-h-screen transform -translate-x-full md:translate-x-0 transition-transform duration-300 ease-in-out fixed md:relative z-30">
            <!-- Sidebar Header -->
            <div class="p-4 border-b border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-industry text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">ERP System</h1>
                        <p class="text-sm text-gray-400">Management Portal</p>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="mt-4 px-4">
                <div class="space-y-2">
                    <!-- Dashboard -->
                    <a href="?module=dashboard"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200 <?php echo $current_module == 'dashboard' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span>Dashboard</span>
                    </a>

                    <!-- Quality Management -->
                    <a href="quality-management/index.php"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-award w-5"></i>
                        <span>Quality Management</span>
                    </a>

                    <!-- Purchase Management -->
                    <a href="purchase-management/index.php"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-shopping-cart w-5"></i>
                        <span>Purchase Management</span>
                    </a>

                    <!-- Accounts -->
                    <a href="#" onclick="showComingSoon('Accounts')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-calculator w-5"></i>
                        <span>Accounts</span>
                    </a>

                    <!-- Sales -->
                    <a href="#" onclick="showComingSoon('Sales')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-chart-line w-5"></i>
                        <span>Sales</span>
                    </a>

                    <!-- Finances -->
                    <a href="#" onclick="showComingSoon('Finances')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-coins w-5"></i>
                        <span>Finances</span>
                    </a>

                    <!-- CRM -->
                    <a href="#" onclick="showComingSoon('CRM')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-users w-5"></i>
                        <span>CRM</span>
                    </a>

                    <!-- HRM -->
                    <a href="#" onclick="showComingSoon('HRM')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-user-tie w-5"></i>
                        <span>HRM</span>
                    </a>

                    <!-- Resource Management -->
                    <a href="#" onclick="showComingSoon('Resource Management')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-boxes w-5"></i>
                        <span>Resource Management</span>
                    </a>

                    <!-- Asset Management -->
                    <a href="#" onclick="showComingSoon('Asset Management')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-building w-5"></i>
                        <span>Asset Management</span>
                    </a>

                    <!-- Compliance Management -->
                    <a href="#" onclick="showComingSoon('Compliance Management')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-shield-alt w-5"></i>
                        <span>Compliance Management</span>
                    </a>

                    <!-- Delivery Management -->
                    <a href="#" onclick="showComingSoon('Delivery Management')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-truck w-5"></i>
                        <span>Delivery Management</span>
                    </a>

                    <!-- Challan Management -->
                    <a href="#" onclick="showComingSoon('Challan Management')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-file-invoice w-5"></i>
                        <span>Challan Management</span>
                    </a>

                    <!-- Production Management -->
                    <a href="production-management/index.php"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-cogs w-5"></i>
                        <span>Production Management</span>
                    </a>

                    <!-- PLC Integration -->
                    <a href="#" onclick="showComingSoon('PLC Integration')"
                        class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-sidebarHover transition-colors duration-200">
                        <i class="fas fa-microchip w-5"></i>
                        <span>PLC Integration</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-btn" class="md:hidden text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bars text-xl"></i>
                        </button>

                        <div>
                            <h2 class="text-2xl font-semibold text-gray-800"><?php echo $page_title; ?></h2>
                            <p class="text-sm text-gray-600"><?php echo $page_subtitle; ?></p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <button class="relative text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bell text-xl"></i>
                            <span
                                class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>

                        <!-- User Profile -->
                        <div class="relative">
                            <button class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                                <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <span class="hidden md:block">Admin</span>
                                <i class="fas fa-chevron-down text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <?php
                // Include module content based on current module
                if ($current_module == 'dashboard') {
                    include 'modules/dashboard.php';
                } else {
                    $module_file = "modules/{$current_module}/{$current_module}.php";
                    if (file_exists($module_file)) {
                        include $module_file;
                    } else {
                        // Show module under development message
                        echo '<div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-tools text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Module Under Development</h3>
                            <p class="text-gray-600 mb-4">The ' . $page_title . ' module is currently being developed.</p>
                            <a href="?module=dashboard" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back to Dashboard
                            </a>
                        </div>';
                    }
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden md:hidden"></div>

    <!-- Scripts -->
    <script src="assets/js/app.js"></script>
</body>

</html>